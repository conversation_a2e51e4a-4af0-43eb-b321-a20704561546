#!/usr/bin/env python3
"""
Phishing Lab - Système de phishing éducatif
Auteur: Assistant IA
Description: Application Flask pour créer et gérer des campagnes de phishing éducatif
"""

import os
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail, Message
from dotenv import load_dotenv
import uuid
import datetime
import json

# Charger les variables d'environnement
load_dotenv()

# Configuration de l'application Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///phishing_lab.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Configuration Flask-Mail
app.config['MAIL_SERVER'] = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
app.config['MAIL_PORT'] = int(os.getenv('SMTP_PORT', 587))
app.config['MAIL_USE_TLS'] = os.getenv('SMTP_USE_TLS', 'True').lower() == 'true'
app.config['MAIL_USERNAME'] = os.getenv('SMTP_USERNAME')
app.config['MAIL_PASSWORD'] = os.getenv('SMTP_PASSWORD')

# Initialisation des extensions
db = SQLAlchemy(app)
mail = Mail(app)

# URL de base pour les liens
BASE_URL = os.getenv('BASE_URL', 'http://localhost:5000')

# Import des modèles après l'initialisation de db
import models
from models import Campaign, Target, Interaction, Credential

# Routes de base
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/campaigns')
def campaigns():
    campaigns = Campaign.query.all()
    return render_template('campaigns.html', campaigns=campaigns)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
