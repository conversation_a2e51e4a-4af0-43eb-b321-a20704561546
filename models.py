"""
Modèles de base de données pour le système de phishing éducatif
"""

from datetime import datetime
import uuid

# Import de db depuis app
from app import db

class Campaign(db.Model):
    """Modèle pour les campagnes de phishing"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    sender_email = db.Column(db.String(100), nullable=False)
    sender_name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    email_template = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='draft')  # draft, sent, completed
    
    # Relations
    targets = db.relationship('Target', backref='campaign', lazy=True, cascade='all, delete-orphan')
    interactions = db.relationship('Interaction', backref='campaign', lazy=True, cascade='all, delete-orphan')

class Target(db.Model):
    """Modèle pour les cibles de la campagne"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    campaign_id = db.Column(db.String(36), db.ForeignKey('campaign.id'), nullable=False)
    email = db.Column(db.String(100), nullable=False)
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    token = db.Column(db.String(36), unique=True, default=lambda: str(uuid.uuid4()))
    email_sent = db.Column(db.Boolean, default=False)
    email_sent_at = db.Column(db.DateTime)
    
    # Relations
    interactions = db.relationship('Interaction', backref='target', lazy=True, cascade='all, delete-orphan')

class Interaction(db.Model):
    """Modèle pour tracker les interactions des utilisateurs"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    campaign_id = db.Column(db.String(36), db.ForeignKey('campaign.id'), nullable=False)
    target_id = db.Column(db.String(36), db.ForeignKey('target.id'), nullable=False)
    token = db.Column(db.String(36), nullable=False)
    interaction_type = db.Column(db.String(50), nullable=False)  # email_open, link_click, form_submit, file_download
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    additional_data = db.Column(db.Text)  # JSON pour données supplémentaires

class Credential(db.Model):
    """Modèle pour stocker les identifiants capturés"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    campaign_id = db.Column(db.String(36), db.ForeignKey('campaign.id'), nullable=False)
    target_id = db.Column(db.String(36), db.ForeignKey('target.id'), nullable=False)
    token = db.Column(db.String(36), nullable=False)
    username = db.Column(db.String(100))
    password = db.Column(db.String(100))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
