{% extends "base.html" %}

{% block title %}Campagnes - Phishing Lab{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-envelope"></i> Campagnes de Phishing</h2>
    <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Nouvelle Campagne
    </a>
</div>

{% if campaigns %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="table-dark">
            <tr>
                <th>Nom</th>
                <th>Expéditeur</th>
                <th>Sujet</th>
                <th>Cibles</th>
                <th>Statut</th>
                <th>C<PERSON><PERSON> le</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for campaign in campaigns %}
            <tr>
                <td>
                    <strong>{{ campaign.name }}</strong>
                    {% if campaign.description %}
                    <br><small class="text-muted">{{ campaign.description[:50] }}{% if campaign.description|length > 50 %}...{% endif %}</small>
                    {% endif %}
                </td>
                <td>
                    {{ campaign.sender_name }}<br>
                    <small class="text-muted">{{ campaign.sender_email }}</small>
                </td>
                <td>{{ campaign.subject }}</td>
                <td>
                    <span class="badge bg-info">{{ campaign.targets|length }} cibles</span>
                </td>
                <td>
                    {% if campaign.status == 'draft' %}
                        <span class="badge bg-secondary">Brouillon</span>
                    {% elif campaign.status == 'sent' %}
                        <span class="badge bg-warning">Envoyée</span>
                    {% elif campaign.status == 'completed' %}
                        <span class="badge bg-success">Terminée</span>
                    {% endif %}
                </td>
                <td>{{ campaign.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" 
                           class="btn btn-outline-primary" title="Voir">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('edit_campaign', campaign_id=campaign.id) }}" 
                           class="btn btn-outline-secondary" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% if campaign.status == 'draft' %}
                        <a href="{{ url_for('send_campaign', campaign_id=campaign.id) }}" 
                           class="btn btn-outline-success" title="Envoyer">
                            <i class="fas fa-paper-plane"></i>
                        </a>
                        {% endif %}
                        <a href="{{ url_for('delete_campaign', campaign_id=campaign.id) }}" 
                           class="btn btn-outline-danger" title="Supprimer"
                           data-confirm="Êtes-vous sûr de vouloir supprimer cette campagne ?">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-envelope fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">Aucune campagne créée</h4>
    <p class="text-muted">Commencez par créer votre première campagne de phishing éducatif.</p>
    <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Créer ma première campagne
    </a>
</div>
{% endif %}
{% endblock %}
