"""
Module de gestion des cibles pour les campagnes de phishing
"""

import csv
import json
import uuid
from io import StringIO
from models import Target, db

class TargetManager:
    """Classe pour gérer l'import et la gestion des cibles"""
    
    @staticmethod
    def import_from_csv(csv_content, campaign_id):
        """
        Importe des cibles depuis un contenu CSV
        Format attendu: email,first_name,last_name
        """
        targets = []
        errors = []
        
        try:
            # Lire le CSV
            csv_reader = csv.DictReader(StringIO(csv_content))
            
            for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 because of header
                try:
                    email = row.get('email', '').strip()
                    first_name = row.get('first_name', '').strip()
                    last_name = row.get('last_name', '').strip()
                    
                    if not email:
                        errors.append(f"Ligne {row_num}: Email manquant")
                        continue
                    
                    # Vérifier si l'email existe déjà pour cette campagne
                    existing = Target.query.filter_by(
                        campaign_id=campaign_id, 
                        email=email
                    ).first()
                    
                    if existing:
                        errors.append(f"Ligne {row_num}: Email {email} déjà présent")
                        continue
                    
                    # Créer la cible
                    target = Target(
                        campaign_id=campaign_id,
                        email=email,
                        first_name=first_name or None,
                        last_name=last_name or None,
                        token=str(uuid.uuid4())
                    )
                    
                    targets.append(target)
                    
                except Exception as e:
                    errors.append(f"Ligne {row_num}: Erreur - {str(e)}")
            
            # Sauvegarder en base
            if targets:
                db.session.add_all(targets)
                db.session.commit()
            
            return {
                'success': True,
                'imported': len(targets),
                'errors': errors,
                'targets': targets
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Erreur lors de l'import: {str(e)}",
                'imported': 0,
                'errors': []
            }
    
    @staticmethod
    def import_from_json(json_content, campaign_id):
        """
        Importe des cibles depuis un contenu JSON
        Format attendu: [{"email": "...", "first_name": "...", "last_name": "..."}]
        """
        targets = []
        errors = []
        
        try:
            data = json.loads(json_content)
            
            if not isinstance(data, list):
                return {
                    'success': False,
                    'error': "Le JSON doit contenir un tableau d'objets",
                    'imported': 0,
                    'errors': []
                }
            
            for index, item in enumerate(data):
                try:
                    email = item.get('email', '').strip()
                    first_name = item.get('first_name', '').strip()
                    last_name = item.get('last_name', '').strip()
                    
                    if not email:
                        errors.append(f"Index {index}: Email manquant")
                        continue
                    
                    # Vérifier si l'email existe déjà pour cette campagne
                    existing = Target.query.filter_by(
                        campaign_id=campaign_id, 
                        email=email
                    ).first()
                    
                    if existing:
                        errors.append(f"Index {index}: Email {email} déjà présent")
                        continue
                    
                    # Créer la cible
                    target = Target(
                        campaign_id=campaign_id,
                        email=email,
                        first_name=first_name or None,
                        last_name=last_name or None,
                        token=str(uuid.uuid4())
                    )
                    
                    targets.append(target)
                    
                except Exception as e:
                    errors.append(f"Index {index}: Erreur - {str(e)}")
            
            # Sauvegarder en base
            if targets:
                db.session.add_all(targets)
                db.session.commit()
            
            return {
                'success': True,
                'imported': len(targets),
                'errors': errors,
                'targets': targets
            }
            
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f"JSON invalide: {str(e)}",
                'imported': 0,
                'errors': []
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Erreur lors de l'import: {str(e)}",
                'imported': 0,
                'errors': []
            }
    
    @staticmethod
    def add_single_target(campaign_id, email, first_name=None, last_name=None):
        """Ajoute une seule cible à la campagne"""
        try:
            # Vérifier si l'email existe déjà
            existing = Target.query.filter_by(
                campaign_id=campaign_id, 
                email=email
            ).first()
            
            if existing:
                return {
                    'success': False,
                    'error': f"L'email {email} existe déjà dans cette campagne"
                }
            
            target = Target(
                campaign_id=campaign_id,
                email=email,
                first_name=first_name,
                last_name=last_name,
                token=str(uuid.uuid4())
            )
            
            db.session.add(target)
            db.session.commit()
            
            return {
                'success': True,
                'target': target
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Erreur lors de l'ajout: {str(e)}"
            }
    
    @staticmethod
    def get_targets_for_campaign(campaign_id):
        """Récupère toutes les cibles d'une campagne"""
        return Target.query.filter_by(campaign_id=campaign_id).all()
    
    @staticmethod
    def delete_target(target_id):
        """Supprime une cible"""
        try:
            target = Target.query.get(target_id)
            if target:
                db.session.delete(target)
                db.session.commit()
                return {'success': True}
            else:
                return {'success': False, 'error': 'Cible non trouvée'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def generate_csv_template():
        """Génère un template CSV pour l'import"""
        return "email,first_name,last_name\<EMAIL>,John,Doe\<EMAIL>,Jane,Smith"
    
    @staticmethod
    def generate_json_template():
        """Génère un template JSON pour l'import"""
        template = [
            {
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe"
            },
            {
                "email": "<EMAIL>",
                "first_name": "Jane",
                "last_name": "Smith"
            }
        ]
        return json.dumps(template, indent=2)
